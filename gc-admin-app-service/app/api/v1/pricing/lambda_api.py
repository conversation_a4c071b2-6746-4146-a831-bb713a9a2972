from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends
from services.pricing.pricing_lambda_service import PricingLambdaService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    path="/pricing-storage-expired",
    summary="[For Lambda] Clean up pricing storage expired",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_CLEAN_UP_PRICING_STORAGE_EXPIRED_SECRET_KEY
        )
    ],
)
@version(1, 0)
@measure_time
async def clean_up_pricing_storage_expired(
    central_db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)]
):
    try:
        redis_cli = await RedisCli.get_instance(configuration)
        pricing_service = PricingLambdaService(central_db_session, redis_cli)
        await pricing_service.clean_up_pricing_storage_expired()
        return ApiResponse.success(
            message="Clean up pricing storage expired successfully"
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error upload_import_file CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Clean up pricing storage expired error: {str(e)}")
        return ApiResponse.error(message="Clean up pricing storage expired failed")
